# 🔨 Instructions de Compilation - Présentation Piano Mattei

## 🎯 Options de Compilation

### **Option 1: Compilation Locale (Recommandée)**

#### **Prérequis**
- **MiKTeX** ou **TeX Live** installé
- **Packages requis**: beamer, tikz, graphicx, adjustbox, colortbl

#### **Commandes**
```bash
# Naviger vers le dossier presentation
cd presentation

# Compilation version moderne (si XeLaTeX disponible)
xelatex main_presentation.tex
xelatex main_presentation.tex

# OU compilation compatible
pdflatex main_presentation_compatible.tex
pdflatex main_presentation_compatible.tex

# OU utiliser le script automatique
compile.bat
```

### **Option 2: Overleaf (En Ligne)**

1. **Créer un nouveau projet** sur [Overleaf](https://www.overleaf.com)
2. **Uploader les fichiers**:
   - `main_presentation_compatible.tex`
   - Dossier `../figures/` avec toutes les images
   - Dossier `../new model data/` avec les graphiques
   - `../preliminaries/logo_university.png`
3. **Compiler** avec pdfLaTeX
4. **Télécharger** le PDF généré

### **Option 3: Installation LaTeX Rapide**

#### **Windows**
```bash
# Installer MiKTeX
winget install MiKTeX.MiKTeX

# OU télécharger depuis https://miktex.org/download
```

#### **macOS**
```bash
# Installer MacTeX
brew install --cask mactex
```

#### **Linux (Ubuntu/Debian)**
```bash
sudo apt-get update
sudo apt-get install texlive-full
```

## 🔧 Résolution des Problèmes Courants

### **Erreur: Package manquant**
```bash
# MiKTeX installe automatiquement les packages manquants
# Pour TeX Live, installer manuellement:
tlmgr install beamer tikz adjustbox colortbl
```

### **Erreur: Images non trouvées**
- Vérifier que les dossiers `../figures/` et `../new model data/` existent
- Vérifier les chemins relatifs dans le fichier .tex
- S'assurer que `logo_university.png` est dans `../preliminaries/`

### **Erreur: Polices non disponibles**
- Utiliser `main_presentation_compatible.tex` au lieu de `main_presentation.tex`
- Cette version utilise les polices standard LaTeX

## 📁 Structure Requise

```
presentation/
├── main_presentation_compatible.tex
├── BUILD_INSTRUCTIONS.md
└── compile.bat

../figures/
├── energie-swot.pdf
├── structure_financement.png
└── workflow_eligibilite.png

../new model data/
├── 07_grant_breakdown_bar_20250608_213739.png
├── 06_lcoe_incentives_comparison_20250608_213739.png
├── 09_project_dashboard_20250608_213739.png
├── 03_revenue_costs_20250608_213739.png
└── docx_kpis_20250608_214202.png

../preliminaries/
└── logo_university.png
```

## 🎯 Résultat Attendu

- **Fichier généré**: `main_presentation.pdf` ou `main_presentation_compatible.pdf`
- **Nombre de slides**: 18 slides
- **Format**: PDF optimisé pour présentation (16:9)
- **Taille**: ~2-5 MB selon les images

## 🚀 Compilation Rapide - Commandes Directes

```bash
# Méthode 1: Compilation simple
pdflatex main_presentation_compatible.tex

# Méthode 2: Double compilation (pour références)
pdflatex main_presentation_compatible.tex
pdflatex main_presentation_compatible.tex

# Méthode 3: Script automatique Windows
./compile.bat
```

## 📞 Support

Si vous rencontrez des problèmes:

1. **Vérifier l'installation LaTeX**: `pdflatex --version`
2. **Vérifier les chemins d'images**: Tous les fichiers sont-ils présents?
3. **Utiliser la version compatible**: `main_presentation_compatible.tex`
4. **Compiler en ligne**: Utiliser Overleaf comme solution de secours

## ✅ Test de Compilation

Pour tester si tout fonctionne:

```bash
# Test rapide
echo "Test LaTeX installation..."
pdflatex --version

# Test compilation
cd presentation
pdflatex -interaction=nonstopmode main_presentation_compatible.tex

# Vérifier le résultat
ls -la *.pdf
```

## 🎨 Personnalisation Finale

Après compilation réussie, vous pouvez:
- Ajuster les couleurs dans le préambule
- Modifier les logos si nécessaire
- Adapter le contenu selon vos besoins
- Exporter en différents formats si requis
