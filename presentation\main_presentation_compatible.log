This is pdfTeX, Version 3.141592653-2.6-1.40.25 (MiKTeX 24.1) (preloaded format=pdflatex 2025.1.20)  14 JUN 2025 21:30
entering extended mode
 restricted \write18 enabled.
 %&-line parsing enabled.
**./main_presentation_compatible.tex
(main_presentation_compatible.tex
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
(D:\miktex\tex/latex/beamer\beamer.cls
Document Class: beamer 2025/02/04 v3.72 A class for typesetting presentations
(D:\miktex\tex/latex/beamer\beamerbasemodes.sty
(D:\miktex\tex/latex/etoolbox\etoolbox.sty
Package: etoolbox 2020/10/05 v2.5k e-TeX tools for LaTeX (JAW)
\etb@tempcnta=\count187
)
\beamer@tempbox=\box51
\beamer@tempcount=\count188
\c@beamerpauses=\count189

(D:\miktex\tex/latex/beamer\beamerbasedecode.sty
\beamer@slideinframe=\count190
\beamer@minimum=\count191
\beamer@decode@box=\box52
)
\beamer@commentbox=\box53
\beamer@modecount=\count192
)
(D:\miktex\tex/generic/iftex\iftex.sty
Package: iftex 2022/02/03 v1.0f TeX engine tests
)
\headdp=\dimen140
\footheight=\dimen141
\sidebarheight=\dimen142
\beamer@tempdim=\dimen143
\beamer@finalheight=\dimen144
\beamer@animht=\dimen145
\beamer@animdp=\dimen146
\beamer@animwd=\dimen147
\beamer@leftmargin=\dimen148
\beamer@rightmargin=\dimen149
\beamer@leftsidebar=\dimen150
\beamer@rightsidebar=\dimen151
\beamer@boxsize=\dimen152
\beamer@vboxoffset=\dimen153
\beamer@descdefault=\dimen154
\beamer@descriptionwidth=\dimen155
\beamer@lastskip=\skip48
\beamer@areabox=\box54
\beamer@animcurrent=\box55
\beamer@animshowbox=\box56
\beamer@sectionbox=\box57
\beamer@logobox=\box58
\beamer@linebox=\box59
\beamer@sectioncount=\count193
\beamer@subsubsectionmax=\count194
\beamer@subsectionmax=\count195
\beamer@sectionmax=\count196
\beamer@totalheads=\count197
\beamer@headcounter=\count198
\beamer@partstartpage=\count199
\beamer@sectionstartpage=\count266
\beamer@subsectionstartpage=\count267
\beamer@animationtempa=\count268
\beamer@animationtempb=\count269
\beamer@xpos=\count270
\beamer@ypos=\count271
\beamer@ypos@offset=\count272
\beamer@showpartnumber=\count273
\beamer@currentsubsection=\count274
\beamer@coveringdepth=\count275
\beamer@sectionadjust=\count276
\beamer@toclastsection=\count277
\beamer@tocsectionnumber=\count278

(D:\miktex\tex/latex/beamer\beamerbaseoptions.sty
(D:\miktex\tex/latex/graphics\keyval.sty
Package: keyval 2022/05/29 v1.15 key=value parser (DPC)
\KV@toks@=\toks17
))
\beamer@paperwidth=\skip49
\beamer@paperheight=\skip50

(D:\miktex\tex/latex/geometry\geometry.sty
Package: geometry 2020/01/02 v5.9 Page Geometry

(D:\miktex\tex/generic/iftex\ifvtex.sty
Package: ifvtex 2019/10/25 v1.7 ifvtex legacy package. Use iftex instead.
)
\Gm@cnth=\count279
\Gm@cntv=\count280
\c@Gm@tempcnt=\count281
\Gm@bindingoffset=\dimen156
\Gm@wd@mp=\dimen157
\Gm@odd@mp=\dimen158
\Gm@even@mp=\dimen159
\Gm@layoutwidth=\dimen160
\Gm@layoutheight=\dimen161
\Gm@layouthoffset=\dimen162
\Gm@layoutvoffset=\dimen163
\Gm@dimlist=\toks18

(D:\miktex\tex/latex/geometry\geometry.cfg))
(D:\miktex\tex/latex/pgf/math\pgfmath.sty
(D:\miktex\tex/latex/pgf/utilities\pgfrcs.sty
(D:\miktex\tex/generic/pgf/utilities\pgfutil-common.tex
\pgfutil@everybye=\toks19
\pgfutil@tempdima=\dimen164
\pgfutil@tempdimb=\dimen165
)
(D:\miktex\tex/generic/pgf/utilities\pgfutil-latex.def
\pgfutil@abb=\box60
)
(D:\miktex\tex/generic/pgf/utilities\pgfrcs.code.tex
(D:\miktex\tex/generic/pgf\pgf.revision.tex)
Package: pgfrcs 2023-01-15 v3.1.10 (3.1.10)
))
(D:\miktex\tex/latex/pgf/utilities\pgfkeys.sty
(D:\miktex\tex/generic/pgf/utilities\pgfkeys.code.tex
\pgfkeys@pathtoks=\toks20
\pgfkeys@temptoks=\toks21

(D:\miktex\tex/generic/pgf/utilities\pgfkeyslibraryfiltered.code.tex
\pgfkeys@tmptoks=\toks22
)))
(D:\miktex\tex/generic/pgf/math\pgfmath.code.tex
(D:\miktex\tex/generic/pgf/math\pgfmathutil.code.tex
\pgf@x=\dimen166
\pgf@xa=\dimen167
\pgf@xb=\dimen168
\pgf@xc=\dimen169
\pgf@y=\dimen170
\pgf@ya=\dimen171
\pgf@yb=\dimen172
\pgf@yc=\dimen173
\c@pgf@counta=\count282
\c@pgf@countb=\count283
\c@pgf@countc=\count284
\c@pgf@countd=\count285
)
(D:\miktex\tex/generic/pgf/math\pgfmathparser.code.tex
\pgfmath@dimen=\dimen174
\pgfmath@count=\count286
\pgfmath@box=\box61
\pgfmath@toks=\toks23
\pgfmath@stack@operand=\toks24
\pgfmath@stack@operation=\toks25
)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.basic.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.trigonometric.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.random.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.comparison.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.base.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.round.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.misc.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfunctions.integerarithmetics.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathcalc.code.tex)
(D:\miktex\tex/generic/pgf/math\pgfmathfloat.code.tex
\c@pgfmathroundto@lastzeros=\count287
)))
(D:\miktex\tex/latex/base\size11.clo
File: size11.clo 2023/05/17 v1.4n Standard LaTeX file (size option)
)
(D:\miktex\tex/latex/pgf/basiclayer\pgfcore.sty
(D:\miktex\tex/latex/graphics\graphicx.sty
Package: graphicx 2021/09/16 v1.2d Enhanced LaTeX Graphics (DPC,SPQR)

(D:\miktex\tex/latex/graphics\graphics.sty
Package: graphics 2022/03/10 v1.4e Standard LaTeX Graphics (DPC,SPQR)

(D:\miktex\tex/latex/graphics\trig.sty
Package: trig 2021/08/11 v1.11 sin cos tan (DPC)
)
(D:\miktex\tex/latex/graphics-cfg\graphics.cfg
File: graphics.cfg 2016/06/04 v1.11 sample graphics configuration
)
Package graphics Info: Driver file: pdftex.def on input line 107.

(D:\miktex\tex/latex/graphics-def\pdftex.def
File: pdftex.def 2022/09/22 v1.2b Graphics/color driver for pdftex
))
\Gin@req@height=\dimen175
\Gin@req@width=\dimen176
)
(D:\miktex\tex/latex/pgf/systemlayer\pgfsys.sty
(D:\miktex\tex/generic/pgf/systemlayer\pgfsys.code.tex
Package: pgfsys 2023-01-15 v3.1.10 (3.1.10)
\pgf@x=\dimen177
\pgf@y=\dimen178
\pgf@xa=\dimen179
\pgf@ya=\dimen180
\pgf@xb=\dimen181
\pgf@yb=\dimen182
\pgf@xc=\dimen183
\pgf@yc=\dimen184
\pgf@xd=\dimen185
\pgf@yd=\dimen186
\w@pgf@writea=\write3
\r@pgf@reada=\read2
\c@pgf@counta=\count288
\c@pgf@countb=\count289
\c@pgf@countc=\count290
\c@pgf@countd=\count291
\t@pgf@toka=\toks26
\t@pgf@tokb=\toks27
\t@pgf@tokc=\toks28
\pgf@sys@id@count=\count292

(D:\miktex\tex/generic/pgf/systemlayer\pgf.cfg
File: pgf.cfg 2023-01-15 v3.1.10 (3.1.10)
)
Driver file for pgf: pgfsys-pdftex.def

(D:\miktex\tex/generic/pgf/systemlayer\pgfsys-pdftex.def
File: pgfsys-pdftex.def 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/generic/pgf/systemlayer\pgfsys-common-pdf.def
File: pgfsys-common-pdf.def 2023-01-15 v3.1.10 (3.1.10)
)))
(D:\miktex\tex/generic/pgf/systemlayer\pgfsyssoftpath.code.tex
File: pgfsyssoftpath.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfsyssoftpath@smallbuffer@items=\count293
\pgfsyssoftpath@bigbuffer@items=\count294
)
(D:\miktex\tex/generic/pgf/systemlayer\pgfsysprotocol.code.tex
File: pgfsysprotocol.code.tex 2023-01-15 v3.1.10 (3.1.10)
))
(D:\miktex\tex/latex/xcolor\xcolor.sty
Package: xcolor 2023/11/15 v3.01 LaTeX color extensions (UK)

(D:\miktex\tex/latex/graphics-cfg\color.cfg
File: color.cfg 2016/01/02 v1.6 sample color configuration
)
Package xcolor Info: Driver file: pdftex.def on input line 274.

(D:\miktex\tex/latex/graphics\mathcolor.ltx)
Package xcolor Info: Model `cmy' substituted by `cmy0' on input line 1350.
Package xcolor Info: Model `hsb' substituted by `rgb' on input line 1354.
Package xcolor Info: Model `RGB' extended on input line 1366.
Package xcolor Info: Model `HTML' substituted by `rgb' on input line 1368.
Package xcolor Info: Model `Hsb' substituted by `hsb' on input line 1369.
Package xcolor Info: Model `tHsb' substituted by `hsb' on input line 1370.
Package xcolor Info: Model `HSB' substituted by `hsb' on input line 1371.
Package xcolor Info: Model `Gray' substituted by `gray' on input line 1372.
Package xcolor Info: Model `wave' substituted by `hsb' on input line 1373.
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcore.code.tex
Package: pgfcore 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/generic/pgf/math\pgfint.code.tex)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepoints.code.tex
File: pgfcorepoints.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@picminx=\dimen187
\pgf@picmaxx=\dimen188
\pgf@picminy=\dimen189
\pgf@picmaxy=\dimen190
\pgf@pathminx=\dimen191
\pgf@pathmaxx=\dimen192
\pgf@pathminy=\dimen193
\pgf@pathmaxy=\dimen194
\pgf@xx=\dimen195
\pgf@xy=\dimen196
\pgf@yx=\dimen197
\pgf@yy=\dimen198
\pgf@zx=\dimen199
\pgf@zy=\dimen256
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepathconstruct.code.tex
File: pgfcorepathconstruct.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@path@lastx=\dimen257
\pgf@path@lasty=\dimen258
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepathusage.code.tex
File: pgfcorepathusage.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@shorten@end@additional=\dimen259
\pgf@shorten@start@additional=\dimen260
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorescopes.code.tex
File: pgfcorescopes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfpic=\box62
\pgf@hbox=\box63
\pgf@layerbox@main=\box64
\pgf@picture@serial@count=\count295
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoregraphicstate.code.tex
File: pgfcoregraphicstate.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgflinewidth=\dimen261
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoretransformations.code.tex
File: pgfcoretransformations.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@pt@x=\dimen262
\pgf@pt@y=\dimen263
\pgf@pt@temp=\dimen264
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorequick.code.tex
File: pgfcorequick.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoreobjects.code.tex
File: pgfcoreobjects.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepathprocessing.code.tex
File: pgfcorepathprocessing.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorearrows.code.tex
File: pgfcorearrows.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfarrowsep=\dimen265
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoreshade.code.tex
File: pgfcoreshade.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@max=\dimen266
\pgf@sys@shading@range@num=\count296
\pgf@shadingcount=\count297
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoreimage.code.tex
File: pgfcoreimage.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoreexternal.code.tex
File: pgfcoreexternal.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfexternal@startupbox=\box65
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorelayers.code.tex
File: pgfcorelayers.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcoretransparency.code.tex
File: pgfcoretransparency.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorepatterns.code.tex
File: pgfcorepatterns.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/generic/pgf/basiclayer\pgfcorerdf.code.tex
File: pgfcorerdf.code.tex 2023-01-15 v3.1.10 (3.1.10)
)))
(D:\miktex\tex/latex/pgf/utilities\xxcolor.sty
Package: xxcolor 2003/10/24 ver 0.1
\XC@nummixins=\count298
\XC@countmixins=\count299
)
(D:\miktex\tex/latex/base\atbegshi-ltx.sty
Package: atbegshi-ltx 2021/01/10 v1.0c Emulation of the original atbegshi
package with kernel methods
)
(D:\miktex\tex/latex/hyperref\hyperref.sty
Package: hyperref 2023-11-26 v7.01g Hypertext links for LaTeX

(D:\miktex\tex/generic/infwarerr\infwarerr.sty
Package: infwarerr 2019/12/03 v1.5 Providing info/warning/error messages (HO)
)
(D:\miktex\tex/latex/kvsetkeys\kvsetkeys.sty
Package: kvsetkeys 2022-10-05 v1.19 Key value parser (HO)
)
(D:\miktex\tex/generic/kvdefinekeys\kvdefinekeys.sty
Package: kvdefinekeys 2019-12-19 v1.6 Define keys (HO)
)
(D:\miktex\tex/generic/pdfescape\pdfescape.sty
Package: pdfescape 2019/12/09 v1.15 Implements pdfTeX's escape features (HO)

(D:\miktex\tex/generic/ltxcmds\ltxcmds.sty
Package: ltxcmds 2023-12-04 v1.26 LaTeX kernel commands for general use (HO)
)
(D:\miktex\tex/generic/pdftexcmds\pdftexcmds.sty
Package: pdftexcmds 2020-06-27 v0.33 Utility functions of pdfTeX for LuaTeX (HO
)
Package pdftexcmds Info: \pdf@primitive is available.
Package pdftexcmds Info: \pdf@ifprimitive is available.
Package pdftexcmds Info: \pdfdraftmode found.
))
(D:\miktex\tex/latex/hycolor\hycolor.sty
Package: hycolor 2020-01-27 v1.10 Color options for hyperref/bookmark (HO)
)
(D:\miktex\tex/latex/letltxmacro\letltxmacro.sty
Package: letltxmacro 2019/12/03 v1.6 Let assignment for LaTeX macros (HO)
)
(D:\miktex\tex/latex/auxhook\auxhook.sty
Package: auxhook 2019-12-17 v1.6 Hooks for auxiliary files (HO)
)
(D:\miktex\tex/latex/hyperref\nameref.sty
Package: nameref 2023-11-26 v2.56 Cross-referencing by name of section

(D:\miktex\tex/latex/refcount\refcount.sty
Package: refcount 2019/12/15 v3.6 Data extraction from label references (HO)
)
(D:\miktex\tex/generic/gettitlestring\gettitlestring.sty
Package: gettitlestring 2019/12/15 v1.6 Cleanup title references (HO)

(D:\miktex\tex/latex/kvoptions\kvoptions.sty
Package: kvoptions 2022-06-15 v3.15 Key value format for package options (HO)
))
\c@section@level=\count300
)
\@linkdim=\dimen267
\Hy@linkcounter=\count301
\Hy@pagecounter=\count302

(D:\miktex\tex/latex/hyperref\pd1enc.def
File: pd1enc.def 2023-11-26 v7.01g Hyperref: PDFDocEncoding definition (HO)
Now handling font encoding PD1 ...
... no UTF-8 mapping file for font encoding PD1
)
(D:\miktex\tex/generic/intcalc\intcalc.sty
Package: intcalc 2019/12/15 v1.3 Expandable calculations with integers (HO)
)
\Hy@SavedSpaceFactor=\count303

(D:\miktex\tex/latex/hyperref\puenc.def
File: puenc.def 2023-11-26 v7.01g Hyperref: PDF Unicode definition (HO)
Now handling font encoding PU ...
... no UTF-8 mapping file for font encoding PU
)
Package hyperref Info: Option `bookmarks' set `true' on input line 4064.
Package hyperref Info: Option `bookmarksopen' set `true' on input line 4064.
Package hyperref Info: Option `implicit' set `false' on input line 4064.
Package hyperref Info: Hyper figures OFF on input line 4181.
Package hyperref Info: Link nesting OFF on input line 4186.
Package hyperref Info: Hyper index ON on input line 4189.
Package hyperref Info: Plain pages OFF on input line 4196.
Package hyperref Info: Backreferencing OFF on input line 4201.
Package hyperref Info: Implicit mode OFF; no redefinition of LaTeX internals.
Package hyperref Info: Bookmarks ON on input line 4448.
\c@Hy@tempcnt=\count304
 (D:\miktex\tex/latex/url\url.sty
\Urlmuskip=\muskip16
Package: url 2013/09/16  ver 3.4  Verb mode for urls, etc.
)
LaTeX Info: Redefining \url on input line 4786.
\XeTeXLinkMargin=\dimen268

(D:\miktex\tex/generic/bitset\bitset.sty
Package: bitset 2019/12/09 v1.3 Handle bit-vector datatype (HO)

(D:\miktex\tex/generic/bigintcalc\bigintcalc.sty
Package: bigintcalc 2019/12/15 v1.5 Expandable calculations on big integers (HO
)
))
\Fld@menulength=\count305
\Field@Width=\dimen269
\Fld@charsize=\dimen270
Package hyperref Info: Hyper figures OFF on input line 6065.
Package hyperref Info: Link nesting OFF on input line 6070.
Package hyperref Info: Hyper index ON on input line 6073.
Package hyperref Info: backreferencing OFF on input line 6080.
Package hyperref Info: Link coloring OFF on input line 6085.
Package hyperref Info: Link coloring with OCG OFF on input line 6090.
Package hyperref Info: PDF/A mode OFF on input line 6095.
\Hy@abspage=\count306


Package hyperref Message: Stopped early.

)
Package hyperref Info: Driver (autodetected): hpdftex.
 (D:\miktex\tex/latex/hyperref\hpdftex.def
File: hpdftex.def 2023-11-26 v7.01g Hyperref driver for pdfTeX

(D:\miktex\tex/latex/base\atveryend-ltx.sty
Package: atveryend-ltx 2020/08/19 v1.0a Emulation of the original atveryend pac
kage
with kernel methods
)
\Fld@listcount=\count307
\c@bookmark@seq@number=\count308

(D:\miktex\tex/latex/rerunfilecheck\rerunfilecheck.sty
Package: rerunfilecheck 2022-07-10 v1.10 Rerun checks for auxiliary files (HO)

(D:\miktex\tex/generic/uniquecounter\uniquecounter.sty
Package: uniquecounter 2019/12/15 v1.4 Provide unlimited unique counter (HO)
)
Package uniquecounter Info: New unique counter `rerunfilecheck' on input line 2
85.
))
(D:\miktex\tex/latex/beamer\beamerbaserequires.sty
(D:\miktex\tex/latex/beamer\beamerbasecompatibility.sty)
(D:\miktex\tex/latex/beamer\beamerbasefont.sty
(D:\miktex\tex/latex/amsfonts\amssymb.sty
Package: amssymb 2013/01/14 v3.01 AMS font symbols

(D:\miktex\tex/latex/amsfonts\amsfonts.sty
Package: amsfonts 2013/01/14 v3.01 Basic AMSFonts support
\@emptytoks=\toks29
\symAMSa=\mathgroup4
\symAMSb=\mathgroup5
LaTeX Font Info:    Redeclaring math symbol \hbar on input line 98.
LaTeX Font Info:    Overwriting math alphabet `\mathfrak' in version `bold'
(Font)                  U/euf/m/n --> U/euf/b/n on input line 106.
))
(D:\miktex\tex/latex/sansmathaccent\sansmathaccent.sty
Package: sansmathaccent 2020/01/31

(D:\miktex\tex/latex/koma-script\scrlfile.sty
Package: scrlfile 2024/10/24 v3.43 KOMA-Script package (file load hooks)

(D:\miktex\tex/latex/koma-script\scrlfile-hook.sty
Package: scrlfile-hook 2024/10/24 v3.43 KOMA-Script package (using LaTeX hooks)


(D:\miktex\tex/latex/koma-script\scrlogo.sty
Package: scrlogo 2024/10/24 v3.43 KOMA-Script package (logo)
)))))
(D:\miktex\tex/latex/beamer\beamerbasetranslator.sty
(D:\miktex\tex/latex/translator\translator.sty
Package: translator 2021-05-31 v1.12d Easy translation of strings in LaTeX
))
(D:\miktex\tex/latex/beamer\beamerbasemisc.sty)
(D:\miktex\tex/latex/beamer\beamerbasetwoscreens.sty)
(D:\miktex\tex/latex/beamer\beamerbaseoverlay.sty
\beamer@argscount=\count309
\beamer@lastskipcover=\skip51
\beamer@trivlistdepth=\count310
)
(D:\miktex\tex/latex/beamer\beamerbasetitle.sty)
(D:\miktex\tex/latex/beamer\beamerbasesection.sty
\c@lecture=\count311
\c@part=\count312
\c@section=\count313
\c@subsection=\count314
\c@subsubsection=\count315
)
(D:\miktex\tex/latex/beamer\beamerbaseframe.sty
\beamer@framebox=\box66
\beamer@frametitlebox=\box67
\beamer@zoombox=\box68
\beamer@zoomcount=\count316
\beamer@zoomframecount=\count317
\beamer@frametextheight=\dimen271
\c@subsectionslide=\count318
\beamer@frametopskip=\skip52
\beamer@framebottomskip=\skip53
\beamer@frametopskipautobreak=\skip54
\beamer@framebottomskipautobreak=\skip55
\beamer@envbody=\toks30
\framewidth=\dimen272
\c@framenumber=\count319
)
(D:\miktex\tex/latex/beamer\beamerbaseverbatim.sty
\beamer@verbatimfileout=\write4
)
(D:\miktex\tex/latex/beamer\beamerbaseframesize.sty
\beamer@splitbox=\box69
\beamer@autobreakcount=\count320
\beamer@autobreaklastheight=\dimen273
\beamer@frametitletoks=\toks31
\beamer@framesubtitletoks=\toks32
)
(D:\miktex\tex/latex/beamer\beamerbaseframecomponents.sty
\beamer@footins=\box70
)
(D:\miktex\tex/latex/beamer\beamerbasecolor.sty)
(D:\miktex\tex/latex/beamer\beamerbasenotes.sty
\beamer@frameboxcopy=\box71
)
(D:\miktex\tex/latex/beamer\beamerbasetoc.sty)
(D:\miktex\tex/latex/beamer\beamerbasetemplates.sty
\beamer@sbttoks=\toks33

(D:\miktex\tex/latex/beamer\beamerbaseauxtemplates.sty
(D:\miktex\tex/latex/beamer\beamerbaseboxes.sty
\bmb@box=\box72
\bmb@colorbox=\box73
\bmb@boxwidth=\dimen274
\bmb@boxheight=\dimen275
\bmb@prevheight=\dimen276
\bmb@temp=\dimen277
\bmb@dima=\dimen278
\bmb@dimb=\dimen279
\bmb@prevheight=\dimen280
)
\beamer@blockheadheight=\dimen281
))
(D:\miktex\tex/latex/beamer\beamerbaselocalstructure.sty
(D:\miktex\tex/latex/tools\enumerate.sty
Package: enumerate 2023/07/04 v3.00 enumerate extensions (DPC)
\@enLab=\toks34
)
\beamer@bibiconwidth=\skip56
\c@figure=\count321
\c@table=\count322
\abovecaptionskip=\skip57
\belowcaptionskip=\skip58
)
(D:\miktex\tex/latex/beamer\beamerbasenavigation.sty
(D:\miktex\tex/latex/beamer\beamerbasenavigationsymbols.tex)
\beamer@section@min@dim=\dimen282
)
(D:\miktex\tex/latex/beamer\beamerbasetheorems.sty
(D:\miktex\tex/latex/amsmath\amsmath.sty
Package: amsmath 2023/05/13 v2.17o AMS math features
\@mathmargin=\skip59

For additional information on amsmath, use the `?' option.
(D:\miktex\tex/latex/amsmath\amstext.sty
Package: amstext 2021/08/26 v2.01 AMS text

(D:\miktex\tex/latex/amsmath\amsgen.sty
File: amsgen.sty 1999/11/30 v2.0 generic functions
\@emptytoks=\toks35
\ex@=\dimen283
))
(D:\miktex\tex/latex/amsmath\amsbsy.sty
Package: amsbsy 1999/11/29 v1.2d Bold Symbols
\pmbraise@=\dimen284
)
(D:\miktex\tex/latex/amsmath\amsopn.sty
Package: amsopn 2022/04/08 v2.04 operator names
)
\inf@bad=\count323
LaTeX Info: Redefining \frac on input line 234.
\uproot@=\count324
\leftroot@=\count325
LaTeX Info: Redefining \overline on input line 399.
LaTeX Info: Redefining \colon on input line 410.
\classnum@=\count326
\DOTSCASE@=\count327
LaTeX Info: Redefining \ldots on input line 496.
LaTeX Info: Redefining \dots on input line 499.
LaTeX Info: Redefining \cdots on input line 620.
\Mathstrutbox@=\box74
\strutbox@=\box75
LaTeX Info: Redefining \big on input line 722.
LaTeX Info: Redefining \Big on input line 723.
LaTeX Info: Redefining \bigg on input line 724.
LaTeX Info: Redefining \Bigg on input line 725.
\big@size=\dimen285
LaTeX Font Info:    Redeclaring font encoding OML on input line 743.
LaTeX Font Info:    Redeclaring font encoding OMS on input line 744.
\macc@depth=\count328
LaTeX Info: Redefining \bmod on input line 905.
LaTeX Info: Redefining \pmod on input line 910.
LaTeX Info: Redefining \smash on input line 940.
LaTeX Info: Redefining \relbar on input line 970.
LaTeX Info: Redefining \Relbar on input line 971.
\c@MaxMatrixCols=\count329
\dotsspace@=\muskip17
\c@parentequation=\count330
\dspbrk@lvl=\count331
\tag@help=\toks36
\row@=\count332
\column@=\count333
\maxfields@=\count334
\andhelp@=\toks37
\eqnshift@=\dimen286
\alignsep@=\dimen287
\tagshift@=\dimen288
\tagwidth@=\dimen289
\totwidth@=\dimen290
\lineht@=\dimen291
\@envbody=\toks38
\multlinegap=\skip60
\multlinetaggap=\skip61
\mathdisplay@stack=\toks39
LaTeX Info: Redefining \[ on input line 2953.
LaTeX Info: Redefining \] on input line 2954.
)
(D:\miktex\tex/latex/amscls\amsthm.sty
Package: amsthm 2020/05/29 v2.20.6
\thm@style=\toks40
\thm@bodyfont=\toks41
\thm@headfont=\toks42
\thm@notefont=\toks43
\thm@headpunct=\toks44
\thm@preskip=\skip62
\thm@postskip=\skip63
\thm@headsep=\skip64
\dth@everypar=\toks45
)
\c@theorem=\count335
)
(D:\miktex\tex/latex/beamer\beamerbasethemes.sty))
(D:\miktex\tex/latex/beamer\beamerthemedefault.sty
(D:\miktex\tex/latex/beamer\beamerfontthemedefault.sty)
(D:\miktex\tex/latex/beamer\beamercolorthemedefault.sty)
(D:\miktex\tex/latex/beamer\beamerinnerthemedefault.sty
\beamer@dima=\dimen292
\beamer@dimb=\dimen293
)
(D:\miktex\tex/latex/beamer\beamerouterthemedefault.sty)))
(D:\miktex\tex/latex/beamer\beamerthemeBoadilla.sty
(D:\miktex\tex/latex/beamer\beamercolorthemerose.sty)
(D:\miktex\tex/latex/beamer\beamerinnerthemerounded.sty)
(D:\miktex\tex/latex/beamer\beamercolorthemedolphin.sty)
(D:\miktex\tex/latex/beamer\beamerouterthemeinfolines.sty))
(D:\miktex\tex/latex/beamer\beamercolorthemewhale.sty)
(D:\miktex\tex/latex/base\inputenc.sty
Package: inputenc 2021/02/14 v1.3d Input encoding file
\inpenc@prehook=\toks46
\inpenc@posthook=\toks47
) (D:\miktex\tex/latex/base\fontenc.sty
Package: fontenc 2021/04/29 v2.0v Standard LaTeX package
)
(D:\miktex\tex/generic/babel\babel.sty
Package: babel 2024/01/07 v24.1 The Babel package
\babel@savecnt=\count336
\U@D=\dimen294
\l@unhyphenated=\language79

(D:\miktex\tex/generic/babel\txtbabel.def)
\bbl@readstream=\read3
LaTeX Info: Redefining \textlatin on input line 3986.
\bbl@dirlevel=\count337

*************************************
* Local config file bblopts.cfg used
*
(D:\miktex\tex/latex/arabi\bblopts.cfg
File: bblopts.cfg 2005/09/08 v0.1 add Arabic and Farsi to "declared" options of
 babel
)
(D:\miktex\tex/generic/babel-french\french.ldf
Language: french 2024-07-25 v3.6c French support from the babel system
Package babel Info: Hyphen rules for 'acadian' set to \l@french
(babel)             (\language22). Reported on input line 91.
Package babel Info: Hyphen rules for 'canadien' set to \l@french
(babel)             (\language22). Reported on input line 92.
\FB@stdchar=\count338
Package babel Info: Making : an active character on input line 421.
Package babel Info: Making ; an active character on input line 422.
Package babel Info: Making ! an active character on input line 423.
Package babel Info: Making ? an active character on input line 424.
\FBguill@level=\count339
\FBold@everypar=\toks48
\FB@Mht=\dimen295
\mc@charclass=\count340
\mc@charfam=\count341
\mc@charslot=\count342
\std@mcc=\count343
\dec@mcc=\count344
\FB@parskip=\dimen296
\listindentFB=\dimen297
\descindentFB=\dimen298
\labelindentFB=\dimen299
\labelwidthFB=\dimen300
\leftmarginFB=\dimen301
\parindentFFN=\dimen302
\FBfnindent=\dimen303
))
(D:\miktex\tex/generic/babel/locale/fr\babel-french.tex
Package babel Info: Importing font and identification data for french
(babel)             from babel-fr.ini. Reported on input line 11.
)
(D:\miktex\tex/latex/carlisle\scalefnt.sty)
Package french.ldf Info: No list customisation for the beamer class,
(french.ldf)             reported on input line 17.

(D:\miktex\tex/latex/pgf/frontendlayer\tikz.sty
(D:\miktex\tex/latex/pgf/basiclayer\pgf.sty
Package: pgf 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/generic/pgf/modules\pgfmoduleshapes.code.tex
File: pgfmoduleshapes.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfnodeparttextbox=\box76
)
(D:\miktex\tex/generic/pgf/modules\pgfmoduleplot.code.tex
File: pgfmoduleplot.code.tex 2023-01-15 v3.1.10 (3.1.10)
)
(D:\miktex\tex/latex/pgf/compatibility\pgfcomp-version-0-65.sty
Package: pgfcomp-version-0-65 2023-01-15 v3.1.10 (3.1.10)
\pgf@nodesepstart=\dimen304
\pgf@nodesepend=\dimen305
)
(D:\miktex\tex/latex/pgf/compatibility\pgfcomp-version-1-18.sty
Package: pgfcomp-version-1-18 2023-01-15 v3.1.10 (3.1.10)
))
(D:\miktex\tex/latex/pgf/utilities\pgffor.sty
(D:\miktex\tex/generic/pgf/utilities\pgffor.code.tex
Package: pgffor 2023-01-15 v3.1.10 (3.1.10)
\pgffor@iter=\dimen306
\pgffor@skip=\dimen307
\pgffor@stack=\toks49
\pgffor@toks=\toks50
))
(D:\miktex\tex/generic/pgf/frontendlayer/tikz\tikz.code.tex
Package: tikz 2023-01-15 v3.1.10 (3.1.10)

(D:\miktex\tex/generic/pgf/libraries\pgflibraryplothandlers.code.tex
File: pgflibraryplothandlers.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgf@plot@mark@count=\count345
\pgfplotmarksize=\dimen308
)
\tikz@lastx=\dimen309
\tikz@lasty=\dimen310
\tikz@lastxsaved=\dimen311
\tikz@lastysaved=\dimen312
\tikz@lastmovetox=\dimen313
\tikz@lastmovetoy=\dimen314
\tikzleveldistance=\dimen315
\tikzsiblingdistance=\dimen316
\tikz@figbox=\box77
\tikz@figbox@bg=\box78
\tikz@tempbox=\box79
\tikz@tempbox@bg=\box80
\tikztreelevel=\count346
\tikznumberofchildren=\count347
\tikznumberofcurrentchild=\count348
\tikz@fig@count=\count349

(D:\miktex\tex/generic/pgf/modules\pgfmodulematrix.code.tex
File: pgfmodulematrix.code.tex 2023-01-15 v3.1.10 (3.1.10)
\pgfmatrixcurrentrow=\count350
\pgfmatrixcurrentcolumn=\count351
\pgf@matrix@numberofcolumns=\count352
)
\tikz@expandcount=\count353

(D:\miktex\tex/generic/pgf/frontendlayer/tikz/libraries\tikzlibrarytopaths.code
.tex
File: tikzlibrarytopaths.code.tex 2023-01-15 v3.1.10 (3.1.10)
))) (D:\miktex\tex/latex/booktabs\booktabs.sty
Package: booktabs 2020/01/12 v1.61803398 Publication quality tables
\heavyrulewidth=\dimen317
\lightrulewidth=\dimen318
\cmidrulewidth=\dimen319
\belowrulesep=\dimen320
\belowbottomsep=\dimen321
\aboverulesep=\dimen322
\abovetopsep=\dimen323
\cmidrulesep=\dimen324
\cmidrulekern=\dimen325
\defaultaddspace=\dimen326
\@cmidla=\count354
\@cmidlb=\count355
\@aboverulesep=\dimen327
\@belowrulesep=\dimen328
\@thisruleclass=\count356
\@lastruleclass=\count357
\@thisrulewidth=\dimen329
)
(D:\miktex\tex/latex/tools\tabularx.sty
Package: tabularx 2023/07/08 v2.11c `tabularx' package (DPC)
 (D:\miktex\tex/latex/tools\array.sty
Package: array 2023/10/16 v2.5g Tabular extension package (FMi)
\col@sep=\dimen330
\ar@mcellbox=\box81
\extrarowheight=\dimen331
\NC@list=\toks51
\extratabsurround=\skip65
\backup@length=\skip66
\ar@cellbox=\box82
)
\TX@col@width=\dimen332
\TX@old@table=\dimen333
\TX@old@col=\dimen334
\TX@target=\dimen335
\TX@delta=\dimen336
\TX@cols=\count358
\TX@ftn=\toks52
)
(D:\miktex\tex/latex/multirow\multirow.sty
Package: multirow 2024/11/12 v2.9 Span multiple rows of a table
\multirow@colwidth=\skip67
\multirow@cntb=\count359
\multirow@dima=\skip68
\bigstrutjot=\dimen337
)
(D:\miktex\tex/latex/eurosym\eurosym.sty
Package: eurosym 1998/08/06 v1.1 European currency symbol ``Euro''
\@eurobox=\box83
)
(D:\miktex\tex/latex/adjustbox\adjustbox.sty
Package: adjustbox 2025/02/26 v1.3c Adjusting TeX boxes (trim, clip, ...)

(D:\miktex\tex/latex/xkeyval\xkeyval.sty
Package: xkeyval 2022/06/16 v2.9 package option processing (HA)

(D:\miktex\tex/generic/xkeyval\xkeyval.tex
(D:\miktex\tex/generic/xkeyval\xkvutils.tex
\XKV@toks=\toks53
\XKV@tempa@toks=\toks54
)
\XKV@depth=\count360
File: xkeyval.tex 2014/12/03 v2.7a key=value parser (HA)
))
(D:\miktex\tex/latex/adjustbox\adjcalc.sty
Package: adjcalc 2012/05/16 v1.1 Provides advanced setlength with multiple back
-ends (calc, etex, pgfmath)
)
(D:\miktex\tex/latex/adjustbox\trimclip.sty
Package: trimclip 2025/02/21 v1.2a Trim and clip general TeX material

(D:\miktex\tex/latex/collectbox\collectbox.sty
Package: collectbox 2022/10/17 v0.4c Collect macro arguments as boxes
\collectedbox=\box84
)
\tc@llx=\dimen338
\tc@lly=\dimen339
\tc@urx=\dimen340
\tc@ury=\dimen341
Package trimclip Info: Using driver 'tc-pdftex.def'.

(D:\miktex\tex/latex/adjustbox\tc-pdftex.def
File: tc-pdftex.def 2025/02/26 v2.3 Clipping driver for pdftex
))
\adjbox@Width=\dimen342
\adjbox@Height=\dimen343
\adjbox@Depth=\dimen344
\adjbox@Totalheight=\dimen345
\adjbox@pwidth=\dimen346
\adjbox@pheight=\dimen347
\adjbox@pdepth=\dimen348
\adjbox@ptotalheight=\dimen349

(D:\miktex\tex/latex/ifoddpage\ifoddpage.sty
Package: ifoddpage 2022/10/18 v1.2 Conditionals for odd/even page detection
\c@checkoddpage=\count361
)
(D:\miktex\tex/latex/varwidth\varwidth.sty
Package: varwidth 2009/03/30 ver 0.92;  Variable-width minipages
\@vwid@box=\box85
\sift@deathcycles=\count362
\@vwid@loff=\dimen350
\@vwid@roff=\dimen351
))
(D:\miktex\tex/latex/caption\subcaption.sty
Package: subcaption 2023/07/28 v1.6b Sub-captions (AR)

(D:\miktex\tex/latex/caption\caption.sty
Package: caption 2023/08/05 v3.6o Customizing captions (AR)

(D:\miktex\tex/latex/caption\caption3.sty
Package: caption3 2023/07/31 v2.4d caption3 kernel (AR)
\caption@tempdima=\dimen352
\captionmargin=\dimen353
\caption@leftmargin=\dimen354
\caption@rightmargin=\dimen355
\caption@width=\dimen356
\caption@indent=\dimen357
\caption@parindent=\dimen358
\caption@hangindent=\dimen359
Package caption Info: beamer document class detected.
Package caption Info: french babel package is loaded.

(D:\miktex\tex/latex/caption\caption-beamer.sto
File: caption-beamer.sto 2022/01/06 v2.0c Adaption of the caption package to th
e beamer document classes (AR)
))
\c@caption@flags=\count363
\c@continuedfloat=\count364
Package caption Info: hyperref package is loaded.
Package caption Info: Hyperref support is turned off
(caption)             because hyperref has stopped early.
)
Package caption Info: New subtype `subfigure' on input line 238.
\c@subfigure=\count365
Package caption Info: New subtype `subtable' on input line 238.
\c@subtable=\count366
)
(D:\miktex\tex/latex/colortbl\colortbl.sty
Package: colortbl 2024/07/06 v1.0i Color table columns (DPC)
\everycr=\toks55
\minrowclearance=\skip69
\rownum=\count367
)
(D:\miktex\tex/latex/psnfss\helvet.sty
Package: helvet 2020/03/25 PSNFSS-v9.3 (WaS) 
) (D:\miktex\tex/latex/psnfss\times.sty
Package: times 2020/03/25 PSNFSS-v9.3 (SPQR) 
)
LaTeX Font Info:    Trying to load font information for T1+phv on input line 10
3.

(D:\miktex\tex/latex/psnfss\t1phv.fd
File: t1phv.fd 2020/03/25 scalable font definitions for T1/phv.
)
(D:\miktex\tex/latex/l3backend\l3backend-pdftex.def
File: l3backend-pdftex.def 2024-01-04 L3 backend support: PDF output (pdfTeX)
\l__color_backend_stack_int=\count368
\l__pdf_internal_box=\box86
)
(main_presentation_compatible.aux)
\openout1 = `main_presentation_compatible.aux'.

LaTeX Font Info:    Checking defaults for OML/cmm/m/it on input line 103.
LaTeX Font Info:    ... okay on input line 103.
LaTeX Font Info:    Checking defaults for OMS/cmsy/m/n on input line 103.
LaTeX Font Info:    ... okay on input line 103.
LaTeX Font Info:    Checking defaults for OT1/cmr/m/n on input line 103.
LaTeX Font Info:    ... okay on input line 103.
LaTeX Font Info:    Checking defaults for T1/cmr/m/n on input line 103.
LaTeX Font Info:    ... okay on input line 103.
LaTeX Font Info:    Checking defaults for TS1/cmr/m/n on input line 103.
LaTeX Font Info:    ... okay on input line 103.
LaTeX Font Info:    Checking defaults for OMX/cmex/m/n on input line 103.
LaTeX Font Info:    ... okay on input line 103.
LaTeX Font Info:    Checking defaults for U/cmr/m/n on input line 103.
LaTeX Font Info:    ... okay on input line 103.
LaTeX Font Info:    Checking defaults for PD1/pdf/m/n on input line 103.
LaTeX Font Info:    ... okay on input line 103.
LaTeX Font Info:    Checking defaults for PU/pdf/m/n on input line 103.
LaTeX Font Info:    ... okay on input line 103.

*geometry* driver: auto-detecting
*geometry* detected driver: pdftex
*geometry* verbose mode - [ preamble ] result:
* driver: pdftex
* paper: custom
* layout: <same size as paper>
* layoutoffset:(h,v)=(0.0pt,0.0pt)
* modes: includehead includefoot 
* h-part:(L,W,R)=(10.95003pt, 433.34402pt, 10.95003pt)
* v-part:(T,H,B)=(0.0pt, 256.0748pt, 0.0pt)
* \paperwidth=455.24408pt
* \paperheight=256.0748pt
* \textwidth=433.34402pt
* \textheight=227.62207pt
* \oddsidemargin=-61.31996pt
* \evensidemargin=-61.31996pt
* \topmargin=-72.26999pt
* \headheight=14.22636pt
* \headsep=0.0pt
* \topskip=11.0pt
* \footskip=14.22636pt
* \marginparwidth=4.0pt
* \marginparsep=10.0pt
* \columnsep=10.0pt
* \skip\footins=10.0pt plus 4.0pt minus 2.0pt
* \hoffset=0.0pt
* \voffset=0.0pt
* \mag=1000
* \@twocolumnfalse
* \@twosidefalse
* \@mparswitchfalse
* \@reversemarginfalse
* (1in=72.27pt=25.4mm, 1cm=28.453pt)

(D:\miktex\tex/context/base/mkii\supp-pdf.mkii
[Loading MPS to PDF converter (version 2006.09.02).]
\scratchcounter=\count369
\scratchdimen=\dimen360
\scratchbox=\box87
\nofMPsegments=\count370
\nofMParguments=\count371
\everyMPshowfont=\toks56
\MPscratchCnt=\count372
\MPscratchDim=\dimen361
\MPnumerator=\count373
\makeMPintoPDFobject=\count374
\everyMPtoPDFconversion=\toks57
) (D:\miktex\tex/latex/epstopdf-pkg\epstopdf-base.sty
Package: epstopdf-base 2020-01-24 v2.11 Base part for package epstopdf
Package epstopdf-base Info: Redefining graphics rule for `.eps' on input line 4
85.

(D:\miktex\tex/latex/00miktex\epstopdf-sys.cfg
File: epstopdf-sys.cfg 2021/03/18 v2.0 Configuration of epstopdf for MiKTeX
))
Package hyperref Info: Link coloring OFF on input line 103.

(main_presentation_compatible.out) (main_presentation_compatible.out)
\@outlinefile=\write5
\openout5 = `main_presentation_compatible.out'.

LaTeX Font Info:    Overwriting symbol font `operators' in version `normal'
(Font)                  OT1/cmr/m/n --> OT1/cmss/m/n on input line 103.
LaTeX Font Info:    Overwriting symbol font `operators' in version `bold'
(Font)                  OT1/cmr/bx/n --> OT1/cmss/b/n on input line 103.
\symnumbers=\mathgroup6
\sympureletters=\mathgroup7
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `normal'
(Font)                  OT1/cmss/m/n --> T1/ptm/m/n on input line 103.
LaTeX Font Info:    Redeclaring math alphabet \mathbf on input line 103.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `normal'
(Font)                  OT1/cmr/bx/n --> T1/phv/b/n on input line 103.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  OT1/cmr/bx/n --> T1/phv/b/n on input line 103.
LaTeX Font Info:    Redeclaring math alphabet \mathsf on input line 103.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `normal'
(Font)                  OT1/cmss/m/n --> T1/phv/m/n on input line 103.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  OT1/cmss/bx/n --> T1/phv/m/n on input line 103.
LaTeX Font Info:    Redeclaring math alphabet \mathit on input line 103.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `normal'
(Font)                  OT1/cmr/m/it --> T1/phv/m/it on input line 103.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  OT1/cmr/bx/it --> T1/phv/m/it on input line 103.
LaTeX Font Info:    Redeclaring math alphabet \mathtt on input line 103.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `normal'
(Font)                  OT1/cmtt/m/n --> T1/pcr/m/n on input line 103.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  OT1/cmtt/m/n --> T1/pcr/m/n on input line 103.
LaTeX Font Info:    Overwriting symbol font `numbers' in version `bold'
(Font)                  T1/phv/m/n --> T1/phv/b/n on input line 103.
LaTeX Font Info:    Overwriting symbol font `pureletters' in version `bold'
(Font)                  T1/phv/m/it --> T1/phv/b/it on input line 103.
LaTeX Font Info:    Overwriting math alphabet `\mathrm' in version `bold'
(Font)                  OT1/cmss/b/n --> T1/ptm/b/n on input line 103.
LaTeX Font Info:    Overwriting math alphabet `\mathbf' in version `bold'
(Font)                  T1/phv/b/n --> T1/phv/b/n on input line 103.
LaTeX Font Info:    Overwriting math alphabet `\mathsf' in version `bold'
(Font)                  T1/phv/m/n --> T1/phv/b/n on input line 103.
LaTeX Font Info:    Overwriting math alphabet `\mathit' in version `bold'
(Font)                  T1/phv/m/it --> T1/phv/b/it on input line 103.
LaTeX Font Info:    Overwriting math alphabet `\mathtt' in version `bold'
(Font)                  T1/pcr/m/n --> T1/pcr/b/n on input line 103.

(D:\miktex\tex/latex/translator\translator-basic-dictionary-English.dict
Dictionary: translator-basic-dictionary, Language: English 
)
(D:\miktex\tex/latex/translator\translator-bibliography-dictionary-English.dict
Dictionary: translator-bibliography-dictionary, Language: English 
)
(D:\miktex\tex/latex/translator\translator-environment-dictionary-English.dict
Dictionary: translator-environment-dictionary, Language: English 
) (D:\miktex\tex/latex/translator\translator-months-dictionary-English.dict
Dictionary: translator-months-dictionary, Language: English 
)
(D:\miktex\tex/latex/translator\translator-numbers-dictionary-English.dict
Dictionary: translator-numbers-dictionary, Language: English 
)
(D:\miktex\tex/latex/translator\translator-theorem-dictionary-English.dict
Dictionary: translator-theorem-dictionary, Language: English 
)
LaTeX Info: Redefining \degres on input line 103.
LaTeX Info: Redefining \up on input line 103.
Package caption Info: Begin \AtBeginDocument code.
Package caption Info: End \AtBeginDocument code.

(main_presentation_compatible.nav)
LaTeX Font Info:    Font shape `T1/phv/m/it' in size <6> not available
(Font)              Font shape `T1/phv/m/sl' tried instead on input line 103.
<../preliminaries/logo_university.png, id=46, 642.4pt x 368.37625pt>
File: ../preliminaries/logo_university.png Graphic file (type png)
<use ../preliminaries/logo_university.png>
Package pdftex.def Info: ../preliminaries/logo_university.png  used on input li
ne 143.
(pdftex.def)             Requested size: 642.39844pt x 368.37534pt.
LaTeX Font Info:    Trying to load font information for U+msa on input line 143
.
 (D:\miktex\tex/latex/amsfonts\umsa.fd
File: umsa.fd 2013/01/14 v3.01 AMS symbols A
)
LaTeX Font Info:    Trying to load font information for U+msb on input line 143
.

(D:\miktex\tex/latex/amsfonts\umsb.fd
File: umsb.fd 2013/01/14 v3.01 AMS symbols B
)
LaTeX Font Info:    Font shape `T1/phv/m/it' in size <10.95> not available
(Font)              Font shape `T1/phv/m/sl' tried instead on input line 143.
LaTeX Font Info:    Font shape `T1/phv/m/it' in size <8> not available
(Font)              Font shape `T1/phv/m/sl' tried instead on input line 143.
 [1

{C:/Users/<USER>/AppData/Local/MiKTeX/fonts/map/pdftex/pdftex.map}{D:/miktex/fon
ts/enc/dvips/base/8r.enc} <../preliminaries/logo_university.png>] (main_present
ation_compatible.toc) [2

]
LaTeX Font Info:    Trying to load font information for TS1+phv on input line 2
00.

(D:\miktex\tex/latex/psnfss\ts1phv.fd
File: ts1phv.fd 2020/03/25 scalable font definitions for TS1/phv.
)
<../figures/energie-swot.pdf, id=121, 368.8972pt x 320.74931pt>
File: ../figures/energie-swot.pdf Graphic file (type pdf)
<use ../figures/energie-swot.pdf>
Package pdftex.def Info: ../figures/energie-swot.pdf  used on input line 200.
(pdftex.def)             Requested size: 368.8963pt x 320.74852pt.

Overfull \vbox (7.48041pt too high) detected at line 200
 []

[3

 <../figures/energie-swot.pdf>]
LaTeX Info: Symbol \textrightarrow not provided by
            font family phv in TS1 encoding.
            Default family used instead on input line 227.
LaTeX Font Info:    Trying to load font information for TS1+cmss on input line 
227.
 (D:\miktex\tex/latex/base\ts1cmss.fd
File: ts1cmss.fd 2023/04/13 v2.5m Standard LaTeX font definitions
)
LaTeX Info: Symbol \textrightarrow not provided by
            font family phv in TS1 encoding.
            Default family used instead on input line 227.
LaTeX Info: Symbol \textrightarrow not provided by
            font family phv in TS1 encoding.
            Default family used instead on input line 227.

[4

]
Overfull \vbox (3.05522pt too high) detected at line 275
 []

[5

]
<../figures/structure_financement.png, id=356, 860.2539pt x 716.9184pt>
File: ../figures/structure_financement.png Graphic file (type png)
<use ../figures/structure_financement.png>
Package pdftex.def Info: ../figures/structure_financement.png  used on input li
ne 317.
(pdftex.def)             Requested size: 860.2518pt x 716.91664pt.

Overfull \vbox (31.61655pt too high) detected at line 317
 []

[6

 <../figures/structure_financement.png>]
<../figures/workflow_eligibilite.png, id=364, 686.565pt x 510.9489pt>
File: ../figures/workflow_eligibilite.png Graphic file (type png)
<use ../figures/workflow_eligibilite.png>
Package pdftex.def Info: ../figures/workflow_eligibilite.png  used on input lin
e 353.
(pdftex.def)             Requested size: 686.56332pt x 510.94765pt.

Overfull \vbox (28.32971pt too high) detected at line 353
 []

[7

 <../figures/workflow_eligibilite.png>]
<../new model data/05_financing_structure_pie_20250608_213739.png, id=410, 860.
2539pt x 716.9184pt>
File: ../new model data/05_financing_structure_pie_20250608_213739.png Graphic 
file (type png)
<use ../new model data/05_financing_structure_pie_20250608_213739.png>
Package pdftex.def Info: ../new model data/05_financing_structure_pie_20250608_
213739.png  used on input line 397.
(pdftex.def)             Requested size: 860.2518pt x 716.91664pt.

Overfull \vbox (11.92732pt too high) detected at line 397
 []

[8

 <../new model data/05_financing_structure_pie_20250608_213739.png>]
<../new model data/docx_kpis_20250608_214202.png, id=419, 713.7867pt x 427.1157
pt>
File: ../new model data/docx_kpis_20250608_214202.png Graphic file (type png)
<use ../new model data/docx_kpis_20250608_214202.png>
Package pdftex.def Info: ../new model data/docx_kpis_20250608_214202.png  used 
on input line 445.
(pdftex.def)             Requested size: 713.78496pt x 427.11465pt.

Overfull \vbox (16.27353pt too high) detected at line 445
 []

[9

 <../new model data/docx_kpis_20250608_214202.png>]
<../new model data/06_lcoe_incentives_comparison_20250608_213739.png, id=470, 1
003.1076pt x 574.7874pt>
File: ../new model data/06_lcoe_incentives_comparison_20250608_213739.png Graph
ic file (type png)
<use ../new model data/06_lcoe_incentives_comparison_20250608_213739.png>
Package pdftex.def Info: ../new model data/06_lcoe_incentives_comparison_202506
08_213739.png  used on input line 482.
(pdftex.def)             Requested size: 1003.10516pt x 574.786pt.
<../new model data/07_grant_breakdown_bar_20250608_213739.png, id=495, 858.5676
pt x 571.4148pt>
File: ../new model data/07_grant_breakdown_bar_20250608_213739.png Graphic file
 (type png)
<use ../new model data/07_grant_breakdown_bar_20250608_213739.png>
Package pdftex.def Info: ../new model data/07_grant_breakdown_bar_20250608_2137
39.png  used on input line 482.
(pdftex.def)             Requested size: 858.56549pt x 571.41339pt.

Overfull \vbox (78.2438pt too high) detected at line 482
 []

[10

 <../new model data/06_lcoe_incentives_comparison_20250608_213739.png> <../new 
model data/07_grant_breakdown_bar_20250608_213739.png>]
<../new model data/09_project_dashboard_20250608_213739.png, id=529, 1149.093pt
 x 853.5087pt>
File: ../new model data/09_project_dashboard_20250608_213739.png Graphic file (
type png)
<use ../new model data/09_project_dashboard_20250608_213739.png>
Package pdftex.def Info: ../new model data/09_project_dashboard_20250608_213739
.png  used on input line 517.
(pdftex.def)             Requested size: 1149.09021pt x 853.50662pt.

Overfull \vbox (13.81256pt too high) detected at line 517
 []

[11

 <../new model data/09_project_dashboard_20250608_213739.png>]
LaTeX Info: Symbol \textrightarrow not provided by
            font family phv in TS1 encoding.
            Default family used instead on input line 561.


! LaTeX Error: Unicode character ✓ (U+2713)
               not set up for use with LaTeX.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.561 \end{frame}
                 
? 
LaTeX Info: Symbol \textrightarrow not provided by
            font family phv in TS1 encoding.
            Default family used instead on input line 561.

! LaTeX Error: Unicode character ✓ (U+2713)
               not set up for use with LaTeX.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.561 \end{frame}
                 
? 
LaTeX Info: Symbol \textrightarrow not provided by
            font family phv in TS1 encoding.
            Default family used instead on input line 561.

! LaTeX Error: Unicode character ✓ (U+2713)
               not set up for use with LaTeX.

See the LaTeX manual or LaTeX Companion for explanation.
Type  H <return>  for immediate help.
 ...                                              
                                                  
l.561 \end{frame}
                 
? 
LaTeX Info: Symbol \textrightarrow not provided by
            font family phv in TS1 encoding.
            Default family used instead on input line 561.
LaTeX Info: Symbol \textrightarrow not provided by
            font family phv in TS1 encoding.
            Default family used instead on input line 561.
LaTeX Info: Symbol \textrightarrow not provided by
            font family phv in TS1 encoding.
            Default family used instead on input line 561.
LaTeX Info: Symbol \textrightarrow not provided by
            font family phv in TS1 encoding.
            Default family used instead on input line 561.

Overfull \vbox (4.47874pt too high) detected at line 561
 []

[12

]
Overfull \vbox (39.22256pt too high) detected at line 608
 []

[13

]
<../new model data/03_revenue_costs_20250608_213739.png, id=637, 1003.1076pt x 
570.4512pt>
File: ../new model data/03_revenue_costs_20250608_213739.png Graphic file (type
 png)
<use ../new model data/03_revenue_costs_20250608_213739.png>
Package pdftex.def Info: ../new model data/03_revenue_costs_20250608_213739.png
  used on input line 668.
(pdftex.def)             Requested size: 1003.10516pt x 570.4498pt.

Overfull \vbox (203.2111pt too high) detected at line 668
 []

[14

 <../new model data/03_revenue_costs_20250608_213739.png>]
\tf@nav=\write6
\openout6 = `main_presentation_compatible.nav'.

\tf@toc=\write7
\openout7 = `main_presentation_compatible.toc'.

\tf@snm=\write8
\openout8 = `main_presentation_compatible.snm'.


(main_presentation_compatible.aux)
 ***********
LaTeX2e <2023-11-01> patch level 1
L3 programming layer <2024-01-04>
 ***********


Package rerunfilecheck Warning: File `main_presentation_compatible.out' has cha
nged.
(rerunfilecheck)                Rerun to get outlines right
(rerunfilecheck)                or use package `bookmark'.

Package rerunfilecheck Info: Checksums for `main_presentation_compatible.out':
(rerunfilecheck)             Before: AE0E2F7F7C67419B2B913F4337EF1913;838
(rerunfilecheck)             After:  7A748BB047A44800799F2747276B6CBE;1312.
 ) 
Here is how much of TeX's memory you used:
 30359 strings out of 474486
 594420 string characters out of 5760170
 1949542 words of memory out of 5000000
 51981 multiletter control sequences out of 15000+600000
 608364 words of font info for 90 fonts, out of 8000000 for 9000
 1141 hyphenation exceptions out of 8191
 128i,15n,131p,405b,1170s stack positions out of 10000i,1000n,20000p,200000b,200000s
 <C:\Users\<USER>\AppData\Local\MiKTeX\fonts/pk/ljfour/jknappen/ec/dpi600\tcss
1095.pk><D:/miktex/fonts/type1/public/amsfonts/cm/cmsy10.pfb><D:/miktex/fonts/t
ype1/urw/helvetic/uhvb8a.pfb><D:/miktex/fonts/type1/urw/helvetic/uhvr8a.pfb><D:
/miktex/fonts/type1/urw/helvetic/uhvro8a.pfb>
Output written on main_presentation_compatible.pdf (14 pages, 2617539 bytes).
PDF statistics:
 704 PDF objects out of 1000 (max. 8388607)
 37 named destinations out of 1000 (max. 500000)
 1255 words of extra memory for PDF output out of 10000 (max. 10000000)

